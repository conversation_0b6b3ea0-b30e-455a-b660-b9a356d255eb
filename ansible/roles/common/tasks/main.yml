---
- name: Update apt cache
  tags: common
  apt:
    update_cache: yes
    cache_valid_time: 3600
  when: ansible_os_family == "Debian"

- name: Install basic packages
  tags: common
  apt:
    name:
      - neovim
      - git
      - curl
      - wget
      - htop
      - iotop
      - net-tools
      - dnsutils
      - iputils-ping
      - mtr
      - tcpdump
      - fail2ban
      - mosh
    state: present
  when: ansible_os_family == "Debian"

- name: Enable IPv4 forwarding
  tags: common
  sysctl:
    name: net.ipv4.ip_forward
    value: "1"
    state: present
    reload: yes

- name: Enable IPv6 forwarding
  tags: common
  sysctl:
    name: net.ipv6.conf.all.forwarding
    value: "1"
    state: present
    reload: yes

- name: Configure timezone
  tags: common
  timezone:
    name: Asia/Shanghai

- name: Configure system locale
  tags: common
  locale_gen:
    name: en_US.UTF-8
    state: present

- name: Set default locale
  tags: common
  lineinfile:
    path: /etc/default/locale
    line: "LANG=en_US.UTF-8"
    state: present
    create: yes

- name: Configure hostname
  tags: common
  hostname:
    name: "{{ inventory_hostname }}"

- name: Install NTP
  tags: common
  apt:
    name: chrony
    state: present
  when: ansible_os_family == "Debian"

- name: Start and enable chrony
  tags: common
  service:
    name: chrony
    state: started
    enabled: yes

- name: Include MOTD tasks
  tags: common, motd
  include_tasks: motd.yml
