global:
  checkNewVersion: true
  sendAnonymousUsage: false

api:
  dashboard: true
  insecure: false

entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entryPoint:
          to: websecure
          scheme: https
  websecure:
    address: ":443"

certificatesResolvers:
  letsencrypt:
    acme:
      email: "{{ traefik_acme_email }}"
      storage: "/var/lib/traefik/acme.json"
      httpChallenge:
        entryPoint: web

providers:
  file:
    directory: "/etc/traefik/dynamic"
    watch: true
  docker:
    endpoint: "unix:///var/run/containerd/containerd.sock"
    watch: true
    exposedByDefault: false
    network: traefik

log:
  level: INFO

accessLog:
  filePath: "/var/lib/traefik/access.log"
  bufferingSize: 100

metrics:
  prometheus:
    entryPoint: metrics 