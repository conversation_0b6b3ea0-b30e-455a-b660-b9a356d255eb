---
- name: Create Traefik directories
  tags: traefik
  file:
    path: "{{ item }}"
    state: directory
    mode: '0755'
  with_items:
    - /etc/traefik
    - /etc/traefik/dynamic
    - /var/lib/traefik

- name: Create Traefik configuration
  tags: traefik
  template:
    src: traefik.yml.j2
    dest: /etc/traefik/traefik.yml
    mode: '0644'

- name: Create Traefik dynamic configuration
  tags: traefik
  template:
    src: dynamic.yml.j2
    dest: /etc/traefik/dynamic/dynamic.yml
    mode: '0644'

- name: Create Traefik compose file
  tags: traefik
  template:
    src: docker-compose.yml.j2
    dest: /etc/traefik/docker-compose.yml
    mode: '0644'

- name: Start Traefik
  tags: traefik
  command: nerdctl compose -f /etc/traefik/docker-compose.yml up -d
  changed_when: true 