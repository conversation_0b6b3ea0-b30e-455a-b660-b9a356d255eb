---
# Air-Gap install for containerd and nerdctl (official binaries)

- name: Setup and install containerd and nerdctl
  tags: containerd
  block:
    - name: Create temporary directory
      tempfile:
        state: directory
        suffix: containerd
      register: temp_dir
      changed_when: false

    - name: Check containerd version
      shell: containerd --version | awk '{print $3}'
      register: containerd_current_version
      changed_when: false
      failed_when: false

    - name: Check runc version
      shell: runc --version | head -1 | awk '{print $3}'
      register: runc_current_version
      changed_when: false
      failed_when: false

    - name: Set runc update flag
      set_fact:
        need_runc_update: >
          {{ runc_current_version.rc != 0 or
             (runc_current_version.stdout | regex_replace('^v', '') != runc_version) }}

    - name: Print runc version
      debug:
        msg: "Current: {{ runc_current_version.stdout | regex_replace('^v', '') }}, Expected: {{ runc_version }}"

    - name: Download runc binary
      get_url:
        url: "https://gh-proxy.com/github.com/opencontainers/runc/releases/download/v{{ runc_version }}/runc.amd64"
        dest: "{{ temp_dir.path }}/runc.amd64"
        mode: '0755'
      when: need_runc_update

    - name: Install runc
      copy:
        src: "{{ temp_dir.path }}/runc.amd64"
        dest: /usr/local/bin/runc
        remote_src: yes
        mode: '0755'
      when: need_runc_update

    - name: Check nerdctl version
      shell: nerdctl version -f '{% raw %}{{.Client.Version}}{% endraw %}'
      register: nerdctl_current_version
      changed_when: false
      failed_when: false
    
    - name: Set containerd update flag
      set_fact:
        need_containerd_update: >
          {{ containerd_current_version.rc != 0 or
             (containerd_current_version.stdout | regex_replace('^v', '') != containerd_version) }}

    - name: Set nerdctl update flag
      set_fact:
        need_nerdctl_update: >
          {{ nerdctl_current_version.rc != 0 or
             (nerdctl_current_version.stdout | regex_replace('^v', '') != nerdctl_version) }}

    - name: Print containerd version
      debug:
        msg: "Current: {{ containerd_current_version.stdout | regex_replace('^v', '') }}, Expected: {{ containerd_version }}"

    - name: Print nerdctl version
      debug:
        msg: "Current: {{ nerdctl_current_version.stdout | regex_replace('^v', '') }}, Expected: {{ nerdctl_version }}"

    - name: Download containerd binary
      get_url:
        url: "https://gh-proxy.com/github.com/containerd/containerd/releases/download/v{{ containerd_version }}/containerd-{{ containerd_version }}-linux-amd64.tar.gz"
        dest: "{{ temp_dir.path }}/containerd-{{ containerd_version }}-linux-amd64.tar.gz"
        mode: '0644'
      when: need_containerd_update

    - name: Download containerd.service
      get_url:
        url: "https://gh-proxy.com/raw.githubusercontent.com/containerd/containerd/main/containerd.service"
        dest: "/usr/lib/systemd/system/containerd.service"
        mode: '0644'
        force: no
      when: need_containerd_update

    - name: Download nerdctl binary
      get_url:
        url: "https://gh-proxy.com/github.com/containerd/nerdctl/releases/download/v{{ nerdctl_version }}/nerdctl-{{ nerdctl_version }}-linux-amd64.tar.gz"
        dest: "{{ temp_dir.path }}/nerdctl-{{ nerdctl_version }}-linux-amd64.tar.gz"
        mode: '0644'
      when: need_nerdctl_update

    - name: Extract containerd
      unarchive:
        src: "{{ temp_dir.path }}/containerd-{{ containerd_version }}-linux-amd64.tar.gz"
        dest: /usr/local
        remote_src: yes
      when: need_containerd_update

    - name: Reload systemd
      systemd:
        daemon_reload: yes
      when: need_containerd_update

    - name: Enable and start containerd
      systemd:
        name: containerd
        enabled: yes
        state: started
      when: need_containerd_update

    - name: Extract nerdctl
      unarchive:
        src: "{{ temp_dir.path }}/nerdctl-{{ nerdctl_version }}-linux-amd64.tar.gz"
        dest: /usr/local/bin
        remote_src: yes
        mode: '0755'
      when: need_nerdctl_update

    - name: Create containerd config directory
      file:
        path: /etc/containerd
        state: directory
        mode: '0755'

    - name: Configure containerd
      template:
        src: config.toml.j2
        dest: /etc/containerd/config.toml
        owner: root
        group: root
        mode: '0644'
      notify: restart containerd

    - name: Create nerdctl config directory
      file:
        path: /etc/nerdctl
        state: directory
        mode: '0755'

    - name: Configure nerdctl
      template:
        src: nerdctl.toml.j2
        dest: /etc/nerdctl/nerdctl.toml
        owner: root
        group: root
        mode: '0644'

    - name: Clean up temporary directory
      file:
        path: "{{ temp_dir.path }}"
        state: absent
      changed_when: false

    - name: Create containerd certs.d directory
      file:
        path: /etc/containerd/certs.d
        state: directory
        mode: '0755'

    - name: Create registry directories for registry mirrors
      file:
        path: "/etc/containerd/certs.d/{{ item }}"
        state: directory
        mode: '0755'
      loop: "{{ registry_mirrors }}"

    - name: Configure registry mirrors
      template:
        src: hosts.toml.j2
        dest: "/etc/containerd/certs.d/{{ item }}/hosts.toml"
        owner: root
        group: root
        mode: '0644'
      vars:
        registry_host: "{{ item if item != 'docker.io' else 'registry-1.docker.io' }}"
      loop: "{{ registry_mirrors }}"
      notify: restart containerd
