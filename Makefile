.PHONY: init deploy clean deploy-proxmox deploy-cloud deploy-apps deploy-monitoring deploy-qddt prepare-local-cache tf-init tf-plan tf-import tf-apply

# 基础目录
ANSIBLE_DIR := ansible
TERRAFORM_DIR := terraform

# 初始化
init:
	@echo "初始化项目..."
	ansible-galaxy install -r $(ANSIBLE_DIR)/requirements.yml
	cd $(TERRAFORM_DIR) && terraform init
# Proxmox 虚拟机部署
deploy-proxmox:
	@echo "部署 Proxmox 虚拟机..."
	cd $(TERRAFORM_DIR) && terraform apply -auto-approve

# 云资源部署（仅用于非 Proxmox 管理的节点）
deploy-cloud:
	@echo "配置云资源..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/cloud.yml

# 基础应用部署
deploy-apps:
	@echo "部署基础应用..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/apps.yml

# 监控系统部署
deploy-monitoring:
	@echo "部署监控系统..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/monitoring.yml

# 青岛控制面部署
deploy-qddt: prepare-local-cache
	@echo "部署青岛控制面..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/qddt.yml$(if $(TAGS), --tags $(TAGS),)

# 完整部署流程
deploy: init deploy-proxmox deploy-cloud deploy-apps deploy-monitoring deploy-qddt

# 清理环境
clean:
	@echo "清理环境..."
	cd $(TERRAFORM_DIR) && terraform destroy -auto-approve 
